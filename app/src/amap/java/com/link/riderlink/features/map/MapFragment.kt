package com.link.riderlink.features.map

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.ToastUtils
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentMapBinding
import com.link.riderlink.features.search.SearchDialogFragment
import com.link.riderlink.utils.system.PermissionsUtil
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.utils.connectivity.isNetConnection
import com.link.riderlink.utils.system.locationPermissions
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.popBackStack

class MapFragment : Fragment() {
    private val viewModel: MapViewModel by viewModels()
    private var _binding: FragmentMapBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    private fun initTheme() {
        changeMap()
    }

    fun changeMap() {
        binding.simpleMap.changeMap()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMapBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.btnBack.setOnClickListener {
            popBackStack()
        }
        init()
        ThemeManager.registerThemeChangeListener(themeCallback)
        initTheme()
    }

    private fun init() {
        binding.simpleMap.setLocateClickListener {
            viewModel.dispatch(MapViewAction.StartLocation)
        }

        binding.simpleMap.setSearchClickListener {
            navigate()
        }

        binding.simpleMap.setOnMapLongClickListener { latitude, longitude ->
            if (!isNetConnection(context)) {
                ToastUtils.show(getString(R.string.no_network_message))
            }
            viewModel.dispatch(
                MapViewAction.GeocodeSearch(
                    requireContext(), latitude,
                    longitude
                )
            )
        }

        viewModel.viewStates.observeState(viewLifecycleOwner, MapViewState::mapState) {
            try {
                binding.simpleMap.changeMap()
            } catch (e: Exception) {
                Log.e(TAG, "onViewCreated: $e")
            }
        }

        viewModel.viewEvents.observeEvent(viewLifecycleOwner) {
            when (it) {
                is MapViewEvent.UpdateLocation -> {
                    Log.d(TAG, "UpdateLocation")
                    if (it.locationInfo.code == 0) {
                        updateCurrentPosition(
                            it.locationInfo.latitude,
                            it.locationInfo.longitude,
                            it.locationInfo.bearing
                        )
                    } else {
                        ToastUtils.show(getString(R.string.location_failed_message))
                    }
                }

                is MapViewEvent.UpdateSearchLocation -> {
                    val locationInfo = it.locationInfo
                    if (locationInfo.code == 1000) {
                        binding.simpleMap.run {
                            addMarker(
                                locationInfo.latitude, locationInfo.longitude,
                                BitmapFactory.decodeResource(resources, R.drawable.location)
                            )
                            setSearchName(locationInfo.address)
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        requestLocation()
    }

    private fun requestLocation() {
        if (XXPermissions.isGranted(requireContext(), locationPermissions)) {
            viewModel.dispatch(MapViewAction.GetLocation)
        } else {
            val listener = {
                XXPermissions.with(requireContext())
                    .permission(Permission.ACCESS_FINE_LOCATION)
                    .permission(Permission.ACCESS_COARSE_LOCATION)
                    .request { _: List<String?>?, all: Boolean ->
                        if (all) {
                            viewModel.dispatch(MapViewAction.GetLocation)
                        } else {
                            ToastUtils.show(getString(R.string.no_permission_message))
                        }
                    }
            }
            PermissionsUtil.showRequestPermissionDialog(requireContext(), listener)
        }
    }


    private fun navigate() {
        if (binding.simpleMap.getSearchName().isEmpty()) {
            val searchDialog = SearchDialogFragment {
                viewModel.dispatch(
                    MapViewAction.OnRoute(
                        requireContext(),
                        it.point_latitude.toDouble(),
                        it.point_longitude.toDouble()
                    ) {
                        this.navigate(R.id.action_mapFragment_to_routeFragment)
                    })
            }
            searchDialog.show(childFragmentManager, "SearchDialog")
        } else {
            this.navigate(R.id.action_mapFragment_to_routeFragment)
        }
    }

    private fun updateCurrentPosition(latitude: Double, longitude: Double, bearing: Float) {
        binding.simpleMap.setPosition(latitude, longitude)
    }


    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }

    companion object {
        private const val TAG = "MapFragment"
    }
}