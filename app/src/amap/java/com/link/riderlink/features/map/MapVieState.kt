package com.link.riderlink.features.map

import android.content.Context
import com.link.rideramap.core.location.domain.entity.LocationInfo

sealed class MapViewEvent {
    data class UpdateLocation(val locationInfo: LocationInfo) : MapViewEvent()
    data class UpdateSearchLocation(val locationInfo: LocationInfo) : MapViewEvent()
}

data class MapViewState(
    val mapState: Int = 0
)

sealed class MapViewAction {
    data class GeocodeSearch(
        val context: Context,
        val latitude: Double,
        val longitude: Double
    ) : MapViewAction()

    data class OnRoute(
        val context: Context,
        val latitude: Double,
        val longitude: Double,
        val callback: () -> Unit
    ) : MapViewAction()

    object GetLocation : MapViewAction()
    object StartLocation : MapViewAction()
}