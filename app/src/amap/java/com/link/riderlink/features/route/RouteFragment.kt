package com.link.riderlink.features.route

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.annotation.ColorInt
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.amap.api.maps.model.LatLng
import com.amap.api.navi.model.NaviPoi
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.ToastUtils
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.databinding.FragmentRouteBinding
import com.link.riderlink.features.search.SearchDialogFragment
import com.link.riderlink.ui.extensions.changeAppearanceStatusBars
import com.link.riderlink.ui.extensions.changeSystemBarColorActually
import com.link.riderlink.ui.extensions.getDestination
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.extensions.saveDestination
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.utils.common.Destination
import com.link.riderlink.utils.system.locationPermissions
import com.link.riderservice.api.dto.WifiStatus
import kotlinx.coroutines.launch


class RouteFragment : Fragment() {
    private val routeViewModel: RouteViewModel by viewModels()
    private var _binding: FragmentRouteBinding? = null
    private lateinit var binding: FragmentRouteBinding

    val themeChangeListener = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            if (routeViewModel.viewStates.value.naviState != NaviState.STARTED) {
                popBackStack()
            } else {
                binding.mapRoute.showExitBar()
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRouteBinding.inflate(inflater, container, false)
        binding = _binding!!
        changeSystemBarColor(
            isNavigationMode = false,
            statusBarNightColor = getColorCompat(R.color.route_status_bar_night),
            navigationBarNightColor = getColorCompat(R.color.route_navigation_bar_night)
        )
        return binding.root
    }

    private fun getColorCompat(colorResourceId: Int) = requireContext().getColor(colorResourceId)

    private fun changeSystemBarColor(
        isNavigationMode: Boolean = false,
        @ColorInt statusBarColor: Int = Color.WHITE,
        @ColorInt navigationBarColor: Int = Color.WHITE,
        @ColorInt statusBarNightColor: Int,
        @ColorInt navigationBarNightColor: Int
    ) {
        val isDay = ThemeManager.themeMode == ThemeManager.ThemeMode.DAY
        val currentStatusBarColor = if (isDay) statusBarColor else statusBarNightColor
        val currentNavigationBarColor = if (isDay) navigationBarColor else navigationBarNightColor
        changeSystemBarColorActually(currentStatusBarColor, currentNavigationBarColor)
        changeAppearanceStatusBars(isNavigationMode)
    }

    private fun init() {
        binding.mapRoute.setNavigateButtonListener {
            Log.d(TAG, "startNavi")
            changeSystemBarColor(
                isNavigationMode = true,
                statusBarColor = getColorCompat(R.color.route_navi_status_bar),
                statusBarNightColor = getColorCompat(R.color.route_navi_status_bar_night),
                navigationBarNightColor = getColorCompat(R.color.route_navi_navigation_bar_night)
            )
            startNavi()
        }

        binding.mapRoute.setStopNaviButtonListener {
            changeSystemBarColor(
                isNavigationMode = false,
                statusBarNightColor = resources.getColor(R.color.route_status_bar_night, null),
                navigationBarNightColor = resources.getColor(
                    R.color.route_navigation_bar_night,
                    null
                )
            )
            routeViewModel.dispatch(RouteViewAction.StopNavi)
        }

        binding.mapRoute.setStartListener {
            val searchDialog = SearchDialogFragment { selectedAddress ->
                updateStartPoint(selectedAddress)
            }
            searchDialog.show(childFragmentManager, "SearchStartDialog")
        }

        binding.mapRoute.setEndListener {
            val searchDialog = SearchDialogFragment { selectedAddress ->
                updateEndPoint(selectedAddress)
            }
            searchDialog.show(childFragmentManager, "SearchEndDialog")
        }

        binding.mapRoute.setBackButtonListener {
            popBackStack()
        }

        binding.mapRoute.setNavi()
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { view, windowInsets ->
            val systemBars = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            view.updatePadding(
                top = systemBars.top,
                bottom = systemBars.bottom
            )
            windowInsets
        }
        init()
        ThemeManager.registerThemeChangeListener(themeChangeListener)
        routeViewModel.viewStates.let { states ->
            states.observeState(viewLifecycleOwner, RouteViewState::status) {
                binding.mapRoute.setNavigateEnable(it.wifiStatus is WifiStatus.DeviceConnected)
            }
            states.observeState(viewLifecycleOwner, RouteViewState::mapState) {
                try {
                    binding.mapRoute.changeMap()
                } catch (e: Exception) {
                    Log.e(TAG, "onViewCreated: $e")
                }
            }
            states.observeState(viewLifecycleOwner, RouteViewState::naviState) {
                when (it) {
                    NaviState.STOPPED, NaviState.DESTINATION -> {
                        binding.mapRoute.stopNaviAnimation()
                    }

                    NaviState.STARTED -> {
                        binding.mapRoute.startNaviAnimation()
                    }

                    else -> {}
                }
            }
            states.observeState(
                viewLifecycleOwner,
                RouteViewState::routesList,
                Lifecycle.State.CREATED
            ) {
                binding.mapRoute.setRouteList(it)
            }
        }

        routeViewModel.viewEvents.observeEvent(viewLifecycleOwner) {
            when (it) {
                is RouteViewEvent.CalculateRouteFail -> {
                    ToastUtils.show(it.message)
                    binding.mapRoute.setNavigateEnable(false)
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                val destination = context?.getDestination()
                val start = RiderLink.instance.getLbsLocation()
                val address = start.address
                val destinationAddress = destination?.address
                val startAddress =
                    if (TextUtils.isEmpty(address)) {
                        start.province.plus(start.city)
                            .plus(start.district)
                    } else {
                        address
                    }
                val latLng = LatLng(
                    start.latitude, start.longitude
                )
                Log.e(TAG, "search: ${destination?.latitude}")
                Log.e(TAG, "search: ${destination?.longitude}")
                Log.e(TAG, "search: ${destination?.address}")
                Log.e(TAG, "search: ${destination?.district}")
                binding.mapRoute.setStart(startAddress)
                binding.mapRoute.setDestination(destinationAddress)
                routeViewModel.dispatch(
                    RouteViewAction.CalculateRideRoute(
                        NaviPoi(
                            start.address, latLng, start.address
                        ), NaviPoi(
                            destination?.address,
                            LatLng(
                                destination?.latitude ?: 0.0,
                                destination?.longitude ?: 0.0
                            ),
                            destination?.address
                        )
                    )
                )
            }
        }
        initTheme()
    }

    private fun startNavi() {
        if (XXPermissions.isGranted(requireContext(), Permission.ACCESS_BACKGROUND_LOCATION)) {
            routeViewModel.dispatch(RouteViewAction.StartNavi)
        } else {
            XXPermissions.with(requireContext()).permission(locationPermissions)
                .permission(Permission.ACCESS_BACKGROUND_LOCATION)
                .request(object : OnPermissionCallback {
                    override fun onDenied(
                        permissions: MutableList<String>,
                        doNotAskAgain: Boolean
                    ) {
                        super.onDenied(permissions, doNotAskAgain)
                        XXPermissions.startPermissionActivity(requireContext(), permissions)
                    }

                    override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                        if (allGranted) {
                            routeViewModel.dispatch(RouteViewAction.StartNavi)
                        }
                    }

                })
        }
    }

    private fun initTheme() {
        val currentThemeResProvider = { resId: Int ->
            ThemeManager.getCurrentThemeRes(requireContext(), resId)
        }
        val autoChangingStringProvider = { day: String, night: String ->
            ThemeManager.autoChangeStr(day, night)
        }
        val nightModeChecker = {
            ThemeManager.isNightMode(requireContext())
        }
        binding.mapRoute.initTheme(
            currentThemeResProvider,
            autoChangingStringProvider,
            nightModeChecker
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        changeSystemBarColorActually(Color.TRANSPARENT, Color.TRANSPARENT)
        ThemeManager.unregisterThemeChangeListener(themeChangeListener)
        _binding = null
    }

    /**
     * 更新起点信息并重新计算路线
     */
    private fun updateStartPoint(selectedAddress: SearchAddress) {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                // 更新起点显示
                binding.mapRoute.setStart(selectedAddress.name)

                // 获取当前终点信息
                val currentDestination = context?.getDestination()

                // 重新计算路线
                if (currentDestination != null) {
                    val startNaviPoi = NaviPoi(
                        selectedAddress.name,
                        LatLng(
                            selectedAddress.point_latitude.toDouble(),
                            selectedAddress.point_longitude.toDouble()
                        ),
                        selectedAddress.name
                    )

                    val endNaviPoi = NaviPoi(
                        currentDestination.address,
                        LatLng(
                            currentDestination.latitude,
                            currentDestination.longitude
                        ),
                        currentDestination.address
                    )

                    routeViewModel.dispatch(
                        RouteViewAction.CalculateRideRoute(startNaviPoi, endNaviPoi)
                    )

                    Log.d(TAG, "重新计算路线: 起点=${selectedAddress.name}")
                } else {
                    ToastUtils.show("未获取到终点信息")
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新起点失败: ${e.message}")
                ToastUtils.show("更新起点失败")
            }
        }
    }

    /**
     * 更新终点信息并重新计算路线
     */
    private fun updateEndPoint(selectedAddress: SearchAddress) {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                // 保存新的目的地信息
                val newDestination = Destination(
                    latitude = selectedAddress.point_latitude.toDouble(),
                    longitude = selectedAddress.point_longitude.toDouble(),
                    address = selectedAddress.name,
                    district = selectedAddress.district
                )
                context?.saveDestination(newDestination)

                // 更新终点显示
                binding.mapRoute.setDestination(selectedAddress.name)

                // 获取当前起点信息
                val currentStart = RiderLink.instance.getLbsLocation()
                val startAddress = if (TextUtils.isEmpty(currentStart.address)) {
                    currentStart.province.plus(currentStart.city).plus(currentStart.district)
                } else {
                    currentStart.address
                }

                // 重新计算路线
                val startNaviPoi = NaviPoi(
                    startAddress,
                    LatLng(currentStart.latitude, currentStart.longitude),
                    startAddress
                )

                val endNaviPoi = NaviPoi(
                    selectedAddress.name,
                    LatLng(
                        selectedAddress.point_latitude.toDouble(),
                        selectedAddress.point_longitude.toDouble()
                    ),
                    selectedAddress.name
                )

                routeViewModel.dispatch(
                    RouteViewAction.CalculateRideRoute(startNaviPoi, endNaviPoi)
                )

                Log.d(TAG, "重新计算路线: 终点=${selectedAddress.name}")
            } catch (e: Exception) {
                Log.e(TAG, "更新终点失败: ${e.message}")
                ToastUtils.show("更新终点失败")
            }
        }
    }

    companion object {
        private const val TAG = "RouteSelectFragment"
    }
}