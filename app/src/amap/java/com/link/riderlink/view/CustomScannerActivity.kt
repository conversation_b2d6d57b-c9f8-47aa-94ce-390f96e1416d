package com.link.riderlink.view

import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.journeyapps.barcodescanner.CaptureManager
import com.journeyapps.barcodescanner.DecoratedBarcodeView
import com.link.riderlink.R
import com.link.riderlink.databinding.ActivityCustomScannerBinding
import kotlin.random.Random


class CustomScannerActivity : AppCompatActivity(), DecoratedBarcodeView.TorchListener {

    private var _binding: ActivityCustomScannerBinding? = null
    private val binding get() = _binding!!
    private lateinit var capture: CaptureManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        _binding = ActivityCustomScannerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, windowInsets ->
            windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            windowInsets
        }
        binding.zxingBarcodeScanner.setTorchListener(this)

        if (!hasFlash()) {
            binding.switchFlashlight.visibility = View.GONE
        }

        capture = CaptureManager(this, binding.zxingBarcodeScanner)
        capture.initializeFromIntent(intent, savedInstanceState)
        capture.setShowMissingCameraPermissionDialog(false)
        capture.decode()
        changeMaskColor()
        changeLaserVisibility(true)
    }

    override fun onResume() {
        super.onResume()
        capture.onResume()
    }

    override fun onPause() {
        super.onPause()
        capture.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        capture.onDestroy()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        capture.onSaveInstanceState(outState)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return binding.zxingBarcodeScanner.onKeyDown(keyCode, event) || super.onKeyDown(
            keyCode,
            event
        )
    }

    private fun hasFlash(): Boolean {
        return applicationContext.packageManager
            .hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH)
    }

    fun changeMaskColor() {
        setRandomMaskColor()
    }

    /**
     * 设置随机颜色（如果需要完全随机的颜色）
     */
    fun setRandomMaskColor() {
        val random = Random.Default
        // 使用HSV色彩空间生成更协调的颜色
        val hue = random.nextFloat() * 360f
        val saturation = 0.5f + random.nextFloat() * 0.5f // 50%-100%
        val value = 0.7f + random.nextFloat() * 0.3f      // 70%-100%

        val color = Color.HSVToColor(120, floatArrayOf(hue, saturation, value))
        binding.zxingBarcodeScanner.viewFinder.setMaskColor(color)
    }

    fun changeLaserVisibility(visible: Boolean) {
        binding.zxingBarcodeScanner.viewFinder.setLaserVisibility(visible)
    }

    fun switchFlashlight(view: View?) {
        if (getString(R.string.turn_on_flashlight) == binding.switchFlashlight.getText()) {
            binding.zxingBarcodeScanner.setTorchOn()
        } else {
            binding.zxingBarcodeScanner.setTorchOff()
        }
    }


    override fun onTorchOn() {
        binding.switchFlashlight.setText(R.string.turn_off_flashlight)
    }

    override fun onTorchOff() {
        binding.switchFlashlight.setText(R.string.turn_on_flashlight)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String?>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        capture.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }
}