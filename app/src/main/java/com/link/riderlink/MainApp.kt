package com.link.riderlink

import android.app.Activity
import android.app.Application
import android.content.ComponentCallbacks
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.hjq.toast.ToastUtils
import com.kongzue.dialogx.DialogX
import com.kongzue.dialogx.DialogX.DEBUGMODE
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.utils.system.CrashHandler
import me.jessyan.autosize.AutoSize
import me.jessyan.autosize.AutoSizeConfig
import me.jessyan.autosize.onAdaptListener
import me.jessyan.autosize.unit.Subunits
import me.jessyan.autosize.utils.AutoSizeLog
import me.jessyan.autosize.utils.ScreenUtils
import java.util.Locale

/**
 * RiderLink应用程序主入口类
 * 负责全局初始化和屏幕适配配置
 */
class MainApp : Application() {

    companion object {
        private const val TAG = "MainApp"

        // 屏幕适配设计尺寸常量
        private const val PORTRAIT_DESIGN_WIDTH = 375
        private const val PORTRAIT_DESIGN_HEIGHT = 812
        private const val LANDSCAPE_DESIGN_WIDTH = 812
        private const val LANDSCAPE_DESIGN_HEIGHT = 375

        // 延迟执行时间
        private const val AUTO_SIZE_DELAY_MS = 500L
    }

    private val mainHandler = Handler(Looper.getMainLooper())
    private var currentActivity: Activity? = null
    private var autoSizeRunnable: Runnable? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Application onCreate")

        try {
            initializeCrashHandler()
            initializeThirdPartyLibraries()
            initializeAutoSizeConfiguration()
            initializeRiderLinkSystem()
            ThemeManager.initialize(
                enablePerformanceMonitoring = BuildConfig.DEBUG // 仅在调试模式启用监控
            )
        } catch (e: Exception) {
            Log.e(TAG, "应用初始化失败", e)
        }
    }

    /**
     * 初始化崩溃处理器
     */
    private fun initializeCrashHandler() {
        try {
            CrashHandler.getInstance().init(applicationContext)
        } catch (e: Exception) {
            Log.e(TAG, "崩溃处理器初始化失败", e)
        }
    }

    /**
     * 初始化第三方库
     */
    private fun initializeThirdPartyLibraries() {
        // 初始化 ToastUtils
        ToastUtils.init(this)

        // 初始化 DialogX
        DEBUGMODE = false
        DialogX.init(this)
    }

    /**
     * 初始化RiderLink系统
     */
    private fun initializeRiderLinkSystem() {
        try {
            RiderLink.instance.init()
        } catch (e: Exception) {
            Log.e(TAG, "RiderLink系统初始化失败", e)
        }
    }

    /**
     * 配置AutoSize屏幕适配
     */
    private fun initializeAutoSizeConfiguration() {
        configureAutoSizeUnits()
        configureAutoSizeListener()
        registerActivityLifecycleCallbacks()
        registerConfigurationChangeCallbacks()
    }

    /**
     * 配置AutoSize单位
     */
    private fun configureAutoSizeUnits() {
        AutoSizeConfig.getInstance().unitsManager
            .setSupportDP(false)
            .setSupportSP(false).supportSubunits = Subunits.MM
    }

    /**
     * 配置AutoSize适配监听器
     */
    private fun configureAutoSizeListener() {
        AutoSizeConfig.getInstance().onAdaptListener = object : onAdaptListener {
            override fun onAdaptBefore(target: Any?, activity: Activity?) {
                activity?.let { safeActivity ->
                    updateScreenSizeConfiguration(safeActivity)
                    updateDesignSizeByOrientation(safeActivity)
                }
            }

            override fun onAdaptAfter(target: Any?, activity: Activity?) {
                AutoSizeLog.d(
                    String.format(
                        Locale.ENGLISH,
                        "%s onAdaptAfter!",
                        target?.javaClass?.name
                    )
                )
            }
        }
    }

    /**
     * 更新屏幕尺寸配置
     */
    private fun updateScreenSizeConfiguration(activity: Activity) {
        // 解决横竖屏切换和分屏模式的屏幕适配问题
        // 注意：ScreenUtils.getScreenSize()的参数不能传Application
        val screenSize = ScreenUtils.getScreenSize(activity)
        AutoSizeConfig.getInstance().apply {
            screenWidth = screenSize[0]
            screenHeight = screenSize[1]
        }
    }

    /**
     * 根据屏幕方向更新设计尺寸
     */
    private fun updateDesignSizeByOrientation(activity: Activity) {
        val config = AutoSizeConfig.getInstance()
        when (activity.resources.configuration.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                config.setDesignWidthInDp(LANDSCAPE_DESIGN_WIDTH)
                    .designHeightInDp = LANDSCAPE_DESIGN_HEIGHT
            }

            else -> {
                config.setDesignWidthInDp(PORTRAIT_DESIGN_WIDTH)
                    .designHeightInDp = PORTRAIT_DESIGN_HEIGHT
            }
        }
    }

    /**
     * 注册Activity生命周期回调
     */
    private fun registerActivityLifecycleCallbacks() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                currentActivity = activity
            }

            override fun onActivityStarted(activity: Activity) {
                currentActivity = activity
            }

            override fun onActivityResumed(activity: Activity) {
                currentActivity = activity
            }

            override fun onActivityPaused(activity: Activity) {
                currentActivity = activity
            }

            override fun onActivityStopped(activity: Activity) {
                currentActivity = activity
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
                currentActivity = activity
            }

            override fun onActivityDestroyed(activity: Activity) {
                if (currentActivity == activity) {
                    currentActivity = null
                }
            }
        })
    }

    /**
     * 注册配置变化回调
     */
    private fun registerConfigurationChangeCallbacks() {
        registerComponentCallbacks(object : ComponentCallbacks {
            override fun onConfigurationChanged(newConfig: Configuration) {
                handleConfigurationChange(newConfig)
            }

            override fun onLowMemory() {
                Log.w(TAG, "系统内存不足")
                // 可以在此处添加内存清理逻辑
            }
        })
    }

    /**
     * 处理配置变化（如横竖屏切换）
     */
    private fun handleConfigurationChange(newConfig: Configuration) {
        val activity = currentActivity ?: return

        // 移除之前的延迟任务
        autoSizeRunnable?.let { mainHandler.removeCallbacks(it) }

        // 创建新的延迟任务
        autoSizeRunnable = Runnable {
            try {
                updateScreenConfiguration(activity, newConfig)
            } catch (e: Exception) {
                Log.e(TAG, "屏幕配置更新失败", e)
            }
        }

        // 延迟执行以确保屏幕尺寸获取正确
        mainHandler.postDelayed(autoSizeRunnable!!, AUTO_SIZE_DELAY_MS)
    }

    /**
     * 更新屏幕配置
     */
    private fun updateScreenConfiguration(activity: Activity, newConfig: Configuration) {
        // 获取当前屏幕的宽高
        val screenSize = ScreenUtils.getScreenSize(activity)
        val config = AutoSizeConfig.getInstance()

        config.apply {
            screenWidth = screenSize[0]
            screenHeight = screenSize[1]
        }

        // 根据屏幕方向设置设计尺寸
        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                config.setDesignWidthInDp(LANDSCAPE_DESIGN_WIDTH)
                    .designHeightInDp = LANDSCAPE_DESIGN_HEIGHT
            }

            else -> {
                config.setDesignWidthInDp(PORTRAIT_DESIGN_WIDTH)
                    .designHeightInDp = PORTRAIT_DESIGN_HEIGHT
            }
        }

        // 重新设置DPI
        AutoSize.autoConvertDensityOfGlobal(activity)
    }
}