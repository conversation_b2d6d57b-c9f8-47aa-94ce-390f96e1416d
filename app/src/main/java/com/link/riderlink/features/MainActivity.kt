package com.link.riderlink.features

import android.app.ActivityManager
import android.app.Dialog
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.link.riderlink.BuildConfig
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.ActivityHomeBinding
import com.link.riderlink.notification.NotifyService
import com.link.riderlink.service.RiderLinkService
import com.link.riderlink.utils.system.AppBackgroundManager
import com.link.riderlink.utils.system.LogcatHelper
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.isNotificationListenerServiceEnabled
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.components.DragFloatActionButton
import com.link.riderlink.ui.components.LockScreenView
import kotlin.system.exitProcess

class MainActivity : AppCompatActivity() {

    // ViewBinding - 使用lazy委托避免内存泄漏
    private var _binding: ActivityHomeBinding? = null
    private val binding get() = _binding!!

    // ViewModel - 管理UI状态
    private val mainViewModel: MainViewModel by viewModels()

    // SharedPreferences - 使用lazy委托
    private val configPreferences: SharedPreferences by lazy {
        applicationContext.getSharedPreferences(PREF_CONFIG_AUTO_CONNECTION, Context.MODE_PRIVATE)
    }

    // 服务Intent
    private var riderLinkServiceIntent: Intent? = null

    // 锁屏Dialog
    private var lockScreenDialog: Dialog? = null

    // 主题变化监听器 - 使用委托模式
    private val themeChangeDelegate = ThemeChangeDelegate()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            initializeWindow()
            initializeBinding()
            initializeComponents()
            observeViewModel()
            setupTheme()
            startServices()
        } catch (e: Exception) {
            Log.e(TAG, "onCreate初始化失败", e)
            handleInitializationError(e)
        }
    }

    /**
     * 初始化窗口设置
     */
    private fun initializeWindow() {
        WindowCompat.setDecorFitsSystemWindows(window, false)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
        }

        window.apply {
            navigationBarColor = Color.TRANSPARENT
            statusBarColor = Color.TRANSPARENT
        }
    }

    /**
     * 初始化ViewBinding
     */
    private fun initializeBinding() {
        _binding = ActivityHomeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, windowInsets ->
            windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            windowInsets
        }
    }

    /**
     * 初始化各种组件
     */
    private fun initializeComponents() {
        // 设置RiderLink实例
        RiderLink.instance.setActivity(this)

        // 初始化日志组件
        initializeLogcat()

        // 设置悬浮按钮
        setupFloatingActionButton()

        // 启动页面延迟隐藏
        scheduleLaunchPageHiding()
    }

    /**
     * 初始化日志组件
     */
    private fun initializeLogcat() {
        val isLogcatEnabled = configPreferences.getBoolean(KEY_LOGCAT_SW, false)
        if (isLogcatEnabled && BuildConfig.DEBUG) {
            LogcatHelper.getInstance(applicationContext).start()
        }
    }

    /**
     * 设置悬浮按钮
     */
    private fun setupFloatingActionButton() {
        binding.fab.setOnFloatButtonClickListener(object :
            DragFloatActionButton.OnFloatButtonCallback {
            override fun onClick() {
                binding.fab.visibility = View.GONE
                showLockScreen()
            }
        })
    }

    /**
     * 延迟隐藏启动页面 - 现在由ViewModel管理
     */
    private fun scheduleLaunchPageHiding() {
        // 启动页面隐藏逻辑已移至ViewModel
        // 通过observeViewModel()观察状态变化
    }

    /**
     * 观察ViewModel状态变化
     */
    private fun observeViewModel() {
        // 监听FAB显示状态
        mainViewModel.uiState.observeState(this, MainUiState::isFabVisible) { isFabVisible ->
            binding.fab.visibility = if (isFabVisible) View.VISIBLE else View.GONE
        }
        // 监听启动页显示状态
        mainViewModel.uiState.observeState(
            this,
            MainUiState::isLaunchPageVisible
        ) { isLaunchPageVisible ->
            binding.launchPage.visibility = if (isLaunchPageVisible) View.VISIBLE else View.GONE
        }
        // 监听错误事件
        mainViewModel.errorEvents.observeEvent(this) { errorMsg ->
            handleError(errorMsg)
        }
    }

    /**
     * 显示锁屏界面
     */
    private fun showLockScreen() {
        if (lockScreenDialog?.isShowing == true) {
            return
        }

        mainViewModel.onLockScreenShown()
        lockScreenDialog = createLockScreenDialog()
        lockScreenDialog?.show()
    }

    /**
     * 创建锁屏Dialog
     */
    private fun createLockScreenDialog(): Dialog {
        val dialog = Dialog(this, R.style.Dialog_Fullscreen).apply {
            setCancelable(false)
            setContentView(R.layout.dialog_lock_screen)
        }

        val window = dialog.window!!
        window.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )

        val lockView: LockScreenView = dialog.findViewById(R.id.lock_view)
        lockView.setOnLSCallback(object : LockScreenView.onLSCallback {
            override fun onLockUp() {
                dialog.dismiss()
                mainViewModel.onLockScreenDismissed()
            }
        })

        WindowCompat.setDecorFitsSystemWindows(window, false)

        // 设置Dialog销毁监听
        dialog.setOnDismissListener {
            lockScreenDialog = null
        }

        return dialog
    }

    /**
     * 设置主题
     */
    private fun setupTheme() {
        ThemeManager.onConfigurationChanged(
            applicationContext,
            ThemeManager.isSystemNightMode(applicationContext)
        )
        ThemeManager.registerThemeChangeListener(themeChangeDelegate)
        initTheme()
    }

    override fun onStart() {
        super.onStart()
        AppBackgroundManager.getInstance().onActivityStarted(<EMAIL>)
        updateThemeMode()
    }

    override fun onResume() {
        super.onResume()
        ensureNotificationServiceRunning()
    }

    override fun onStop() {
        super.onStop()
        AppBackgroundManager.getInstance().onActivityStopped()
    }

    override fun onDestroy() {
        super.onDestroy()
        cleanup()
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            // 停止服务
            stopRiderLinkService()

            // 清理RiderLink实例
            RiderLink.instance.destroy()

            // 停止日志服务
            if (BuildConfig.DEBUG) {
                LogcatHelper.getInstance(applicationContext).stop()
            }

            // 解注册主题监听器
            ThemeManager.unregisterThemeChangeListener(themeChangeDelegate)

            // 清理Dialog
            lockScreenDialog?.dismiss()
            lockScreenDialog = null

            // 清理ViewBinding
            _binding = null

        } catch (e: Exception) {
            Log.e(TAG, "清理资源时发生错误", e)
        } finally {
            exitProcess(0)
        }
    }

    /**
     * 更新主题模式
     */
    private fun updateThemeMode() {
        ThemeManager.themeMode = if (ThemeManager.isNightMode(applicationContext)) {
            ThemeManager.ThemeMode.NIGHT
        } else {
            ThemeManager.ThemeMode.DAY
        }
    }

    /**
     * 确保通知服务运行
     */
    private fun ensureNotificationServiceRunning() {
        if (!isNotificationListenerServiceEnabled()) {
            return
        }

        if (!isNotificationServiceRunning()) {
            Log.d(TAG, "通知服务未运行，正在重启...")
            restartNotificationService()
        }
    }

    /**
     * 检查通知服务是否运行
     */
    private fun isNotificationServiceRunning(): Boolean {
        val componentName = ComponentName(this, NotifyService::class.java)
        val manager = getSystemService(ACTIVITY_SERVICE) as ActivityManager
        val runningServices = manager.getRunningServices(Int.MAX_VALUE) ?: return false

        return runningServices.any { service ->
            service.service == componentName && service.pid == android.os.Process.myPid()
        }
    }

    /**
     * 重启通知服务
     */
    private fun restartNotificationService() {
        val componentName = ComponentName(this, NotifyService::class.java)
        val pm = packageManager

        pm.setComponentEnabledSetting(
            componentName,
            PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
            PackageManager.DONT_KILL_APP
        )

        pm.setComponentEnabledSetting(
            componentName,
            PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
            PackageManager.DONT_KILL_APP
        )
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        ThemeManager.onConfigurationChanged(applicationContext, newConfig.isNightModeActive)
    }

    /**
     * 初始化主题
     */
    private fun initTheme() {
        binding.launchImg.setImageResource(
            ThemeManager.getCurrentThemeRes(applicationContext, R.drawable.launch_bitmap)
        )
        binding.launchPage.setBackgroundColor(
            resources.getColor(
                ThemeManager.getCurrentThemeRes(applicationContext, R.color.launch_page),
                null
            )
        )

        val naviType = if (ThemeManager.themeMode == ThemeManager.ThemeMode.DAY) {
            NAVI_DAYTIME
        } else {
            NAVI_NIGHT
        }
        RiderLink.instance.changeVirtualNaviMap(naviType, true)
        updateSystemBarsAppearance()
    }

    /**
     * 更新系统栏外观
     */
    private fun updateSystemBarsAppearance() {
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        val isLightMode = ThemeManager.themeMode == ThemeManager.ThemeMode.DAY

        windowInsetsController.apply {
            isAppearanceLightNavigationBars = isLightMode
            isAppearanceLightStatusBars = isLightMode
        }
    }

    /**
     * 启动RiderLink服务
     */
    private fun startServices() {
        riderLinkServiceIntent = Intent(this, RiderLinkService::class.java)
        startService(riderLinkServiceIntent)
    }

    /**
     * 停止RiderLink服务
     */
    private fun stopRiderLinkService() {
        riderLinkServiceIntent?.let {
            stopService(it)
            riderLinkServiceIntent = null
        }
    }

    /**
     * 处理初始化错误
     */
    private fun handleInitializationError(error: Exception) {
        // 可以显示错误对话框或重试机制
        Log.e(TAG, "应用初始化失败，准备退出", error)
        finish()
    }

    /**
     * 处理错误
     */
    private fun handleError(error: String) {
        Log.e(TAG, "发生错误: $error")
        // 可以显示错误提示给用户，如Toast、Dialog等
    }

    /**
     * 主题变化委托类
     */
    private inner class ThemeChangeDelegate : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }

        override fun onThemeBarChanged(show: Boolean, colorBar: Int) {
            // 处理主题栏变化
        }
    }

    companion object {
        private const val TAG = "MainActivity"
        private const val NAVI_DAYTIME = 0
        private const val NAVI_NIGHT = 1
        private const val LAUNCH_PAGE_DELAY = 2000L
        private const val PREF_CONFIG_AUTO_CONNECTION = "config_auto_connection"
        private const val KEY_LOGCAT_SW = "logcat_sw"
    }
}