package com.link.riderlink.features.my

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.core.content.edit
import androidx.fragment.app.Fragment
import com.hjq.toast.ToastUtils
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentSettingBinding
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeHelper
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.ThemeResourceHelper
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderlink.ui.components.dialogs.SelectDialog
import com.link.riderservice.api.RiderService

class SettingFragment : Fragment() {
    private var _binding: FragmentSettingBinding? = null
    private val binding get() = _binding!!

    private val TAG = "SettingFragment"
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val preference =
            context?.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val modeNight = preference?.getBoolean("mode_night", false) == true
        val followSystem = preference?.getBoolean("follow_system", false) == true
        binding.btnSwitchMode.isChecked = modeNight
        binding.btnSwitchFollow.isChecked = if (modeNight) followSystem else modeNight
        binding.btnSwitchFollow.isEnabled = modeNight
        binding.btnSwitchMode.setOnClickListener {
            val isChecked = binding.btnSwitchMode.isChecked
            binding.btnSwitchFollow.isEnabled = isChecked
            ThemeManager.themeMode =
                if (isChecked) ThemeManager.ThemeMode.NIGHT else ThemeManager.ThemeMode.DAY
            if (!isChecked) {
                preference?.edit {
                    putBoolean("follow_system", false)
                }
                binding.btnSwitchFollow.isChecked = false
            }
            preference?.edit {
                putBoolean("mode_night", isChecked)
            }
        }
        binding.btnSwitchFollow.setOnClickListener {
            val isChecked = binding.btnSwitchFollow.isChecked
            ThemeManager.themeMode =
                if (ThemeManager.isSystemNightMode(requireContext())) ThemeManager.ThemeMode.NIGHT else ThemeManager.ThemeMode.DAY
            if (!isChecked) {
                ThemeManager.themeMode =
                    ThemeManager.ThemeMode.NIGHT
            }
            preference?.edit {
                putBoolean("follow_system", isChecked)
            }
        }
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        binding.itemHelp.setOnClickListener {
            navigate(R.id.action_settingFragment_to_helpFragment)
        }
        binding.itemPrivate.setOnClickListener {
            navigate(R.id.action_settingFragment_to_privacyFragment)
        }
        binding.itemAbout.setOnClickListener {
            navigate(R.id.action_settingFragment_to_aboutFragment)
        }
        binding.itemDel.setOnClickListener {
            val dialog = SelectDialog.Builder(requireActivity())
            val sharedPref = RiderService.instance.getApplication()
                .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
            val wifiAddress = sharedPref.getString("wifi_address", "")
            val bleAddress = sharedPref.getString("ble_address", "")
            val port = sharedPref.getInt("wifi_port", 0)
            var message = ""
            if (wifiAddress != null && port != 0) {
                message = "\nble:$bleAddress\nwifi_address:$wifiAddress\nport:$port"
            }
            dialog
                .setTitle(getString(R.string.delete_config_title))
                .setMessage(getString(R.string.delete_config_message, message))
                .setOnConfirmClick {
                    RiderLink.instance.disconnect(false)
                    RiderService.instance.deleteConfig()
                    ToastUtils.show(getString(R.string.delete_config_success))
                }.show()
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    private fun initTheme() {
        setupBackButton(binding.ibBack)
        binding.setRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)

        ThemeHelper.setSettingPageTextTheme(
            binding.txTitle,
            binding.tvNight,
            binding.tvFollow,
            binding.tvHelp,
            binding.tvPrivate,
            binding.tvAbout,
            binding.tvDel
        )
        ThemeResourceHelper.setSettingPageBackgrounds(
            binding.background1,
            binding.background2,
            binding.background3,
            binding.background4
        )
        ThemeResourceHelper.setSettingPageIcons(
            binding.setNight,
            binding.setHelp,
            binding.setPrivate,
            binding.setAbout,
            binding.setDel
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
    }
}