package com.link.riderlink.ui.extensions

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import androidx.datastore.core.CorruptionException
import androidx.datastore.core.DataStore
import androidx.datastore.core.Serializer
import androidx.datastore.dataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import androidx.datastore.preferences.protobuf.InvalidProtocolBufferException
import com.link.riderlink.protobuf.UserPreferences
import com.link.riderlink.utils.common.Destination
import com.link.riderservice.api.dto.RiderServiceConfig
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
import java.io.InputStream
import java.io.OutputStream

val KEY_DESTINATION_LATITUDE = doublePreferencesKey("key_latitude")
val KEY_DESTINATION_LONGITUDE = doublePreferencesKey("key_longitude")
val KEY_DESTINATION_ADDRESS = stringPreferencesKey("key_address")
val KEY_DESTINATION_DISTRICT = stringPreferencesKey("key_district")
val IS_AGREE_POLICY = booleanPreferencesKey("is_policy")
val IS_LOCATION_DENIED = booleanPreferencesKey("is_location_denied")
val IS_BLUETOOTH_DENIED = booleanPreferencesKey("is_bluetooth_denied")
val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "rider_data")

suspend fun Context.putString(key: Preferences.Key<String>, value: String) = dataStore.edit {
    it[key] = value
}

fun Context.getSting(key: Preferences.Key<String>, default: String? = null): String = runBlocking {
    return@runBlocking dataStore.data.map {
        it[key] ?: default
    }.first()!!
}

suspend fun Context.putBoolean(key: Preferences.Key<Boolean>, value: Boolean) = dataStore.edit {
    it[key] = value
}


fun Context.getBoolean(key: Preferences.Key<Boolean>, default: Boolean = false): Boolean =
    runBlocking {
        return@runBlocking dataStore.data.map {
            it[key] ?: default
        }.first()
    }


suspend fun Context.saveDestination(destination: Destination) {
    dataStore.edit {
        it[KEY_DESTINATION_LATITUDE] = destination.latitude
        it[KEY_DESTINATION_LONGITUDE] = destination.longitude
        it[KEY_DESTINATION_ADDRESS] = destination.address
        it[KEY_DESTINATION_DISTRICT] = destination.district
    }
}

fun Context.getDestination(): Destination = runBlocking {
    return@runBlocking dataStore.data.map {
        val latitude = it[KEY_DESTINATION_LATITUDE] ?: 0.0
        val longitude = it[KEY_DESTINATION_LONGITUDE] ?: 0.0
        val address = it[KEY_DESTINATION_ADDRESS] ?: ""
        val district = it[KEY_DESTINATION_DISTRICT] ?: ""
        return@map Destination(latitude, longitude, address, district)
    }.first()
}


object UserPreferencesSerializer : Serializer<UserPreferences> {
    override val defaultValue: UserPreferences
        get() = UserPreferences.getDefaultInstance()

    override suspend fun readFrom(input: InputStream): UserPreferences {
        try {
            return UserPreferences.parseFrom(input)
        } catch (exception: InvalidProtocolBufferException) {
            throw CorruptionException("Cannot read proto.", exception)
        }
    }

    override suspend fun writeTo(t: UserPreferences, output: OutputStream) {
        t.writeTo(output)
    }
}

val Context.userPreferencesDataStore: DataStore<UserPreferences> by dataStore(
    fileName = "user_prefs.pb",
    serializer = UserPreferencesSerializer
)

suspend fun Context.updateUserPreferences(configNotification: RiderServiceConfig) {
    userPreferencesDataStore.updateData { currentPreferences ->
        currentPreferences.toBuilder()
            .setIsSupportDvr(configNotification.isSupportDvr)
            .setIsSupportNavi(configNotification.isSupportNavi)
            .setIsSupportScreenNavi(configNotification.isSupportScreenNavi)
            .setIsSupportWeather(configNotification.isSupportWeather)
            .setIsSupportNotification(configNotification.isSupportNotification)
            .setIsSupportCircularScreen(configNotification.isSupportCircularScreen)
            .setIsSupportCruise(configNotification.isSupportCruise)
            .build()
    }
}

fun Context.isNotificationListenerServiceEnabled(): Boolean {
    val packageNames = NotificationManagerCompat.getEnabledListenerPackages(this)
    return packageNames.contains(packageName)
}

fun Context.isChina(): Boolean {
    return "zh".equals(resources.configuration.locale)
}