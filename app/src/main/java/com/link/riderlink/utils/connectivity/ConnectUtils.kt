package com.link.riderlink.utils.connectivity

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build


/**
 * 判断当前是否有网络连接
 * 使用现代化的NetworkCapabilities API，支持更准确的网络状态检测
 * @param context 上下文
 * @return true表示有可用的网络连接，false表示无网络连接
 */
fun isNetConnection(context: Context?): Boolean {
    context ?: return false

    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        ?: return false

    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        // 使用现代API (API 23+)
        val activeNetwork = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

        // 检查网络是否具备基本的传输能力
        val hasTransport = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)

        // 检查网络是否具备互联网访问能力
        val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)

        // 检查网络是否已验证（可选，更严格的检查）
        val isValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)

        hasTransport && hasInternet && isValidated
    } else {
        // 向后兼容 (API < 23，但由于minSdk=24，这部分代码实际不会执行)
        @Suppress("DEPRECATION")
        val networkInfo = connectivityManager.activeNetworkInfo
        networkInfo?.isConnected == true
    }
}

/**
 * 检查是否连接到WiFi网络
 * @param context 上下文
 * @return true表示连接到WiFi，false表示未连接WiFi
 */
fun isWifiConnected(context: Context?): Boolean {
    context ?: return false

    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        ?: return false

    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        val activeNetwork = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

        networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) &&
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    } else {
        @Suppress("DEPRECATION")
        val networkInfo = connectivityManager.activeNetworkInfo
        networkInfo?.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
    }
}

/**
 * 检查是否连接到移动数据网络
 * @param context 上下文
 * @return true表示连接到移动数据，false表示未连接移动数据
 */
fun isMobileConnected(context: Context?): Boolean {
    context ?: return false

    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        ?: return false

    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        val activeNetwork = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

        networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) &&
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    } else {
        @Suppress("DEPRECATION")
        val networkInfo = connectivityManager.activeNetworkInfo
        networkInfo?.type == ConnectivityManager.TYPE_MOBILE && networkInfo.isConnected
    }
}